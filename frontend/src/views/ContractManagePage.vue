<template>
  <div class="contract-manage-page">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-left">
        <h1 class="page-title">合同管理</h1>
        <p class="page-subtitle">管理系统中的所有合同</p>
      </div>
      <div class="header-right">
        <el-button type="primary" @click="exportContracts">
          <el-icon><Download /></el-icon>
          导出数据
        </el-button>
      </div>
    </div>

    <!-- 统计卡片 -->
    <div class="stats-section">
      <div class="stats-grid">
        <div
          v-for="card in statsCards"
          :key="card.key"
          class="stat-card clickable-card"
          :class="[`stat-card--${card.color}`, { 'active': activeFilter === card.filter }]"
          @click="setActiveFilter(card.filter)"
        >
          <div class="stat-icon">
            <el-icon :size="32">
              <component :is="card.icon" />
            </el-icon>
          </div>
          <div class="stat-content">
            <div class="stat-value">{{ card.value }}</div>
            <div class="stat-label">{{ card.label }}</div>
          </div>
        </div>
      </div>
    </div>

    <!-- 主要内容 -->
    <div class="page-content">
      <el-card class="list-card">
        <ContractList
          ref="contractListRef"
          :show-selection="true"
          filter-role="admin"
          @view-contract="handleViewContract"
          @view-contract-tab="handleViewContractInTab"
          @edit-contract="handleEditContract"
          @review-contract="handleReviewContract"
          @delete-contract="handleDeleteContract"
          @selection-change="handleSelectionChange"
          @row-click="handleRowClick"
        >
          <template #actions>
            <el-button
              v-if="selectedContracts.length > 0"
              type="danger"
              @click="batchDelete"
            >
              批量删除 ({{ selectedContracts.length }})
            </el-button>
          </template>
        </ContractList>
      </el-card>
    </div>

    <!-- 合同详情对话框 -->
    <ContractDetailDialog
      v-model="showDetailDialog"
      :contract-id="currentContractId"
      @updated="handleContractUpdated"
    />

    <!-- 合同编辑对话框 -->
    <ContractEditDialog
      v-model="showEditDialog"
      :contract="currentContract"
      @updated="handleContractUpdated"
    />

    <!-- 合同审核对话框 -->
    <ContractReviewDialog
      v-model="showReviewDialog"
      :contract="currentContract"
      @reviewed="handleContractReviewed"
    />
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from "vue";
import { ElMessage, ElMessageBox } from "element-plus";
import {
  Download,
  Document,
  Clock,
  Check,
  Close,
  EditPen,
  FolderOpened,
} from "@element-plus/icons-vue";

import ContractList from "@/components/contract/ContractList.vue";
import ContractDetailDialog from "@/components/contract/ContractDetailDialog.vue";
import ContractEditDialog from "@/components/contract/ContractEditDialog.vue";
import ContractReviewDialog from "@/components/contract/ContractReviewDialog.vue";

import { useContracts } from "@/composables/useContracts";
import { useTabs } from "@/composables/useTabs";
import { useUserStore } from "@/stores/user";

// 合同管理
const { stats, getStats, deleteContract } = useContracts();

// Tab管理
const { openTab } = useTabs();

// 用户信息
const userStore = useUserStore();
const userRole = computed(() => userStore.userRole);
const isLegalOfficer = computed(() => userRole.value === "legal_officer");

// 对话框状态
const showDetailDialog = ref(false);
const showEditDialog = ref(false);
const showReviewDialog = ref(false);

// 当前操作的合同
const currentContract = ref(null);
const currentContractId = ref(null);

// 选中的合同
const selectedContracts = ref([]);

// 当前筛选条件
const activeFilter = ref("");

// 合同列表引用
const contractListRef = ref();

// 根据用户角色生成统计卡片
const statsCards = computed(() => {
  if (isLegalOfficer.value) {
    // 法规员卡片：分配给我、待编号、已编号
    return [
      {
        key: 'total',
        label: '分配给我',
        value: stats.value.total || 0,
        icon: FolderOpened,
        color: 'primary',
        filter: 'legal_assigned'
      },
      {
        key: 'pending_number',
        label: '待编号',
        value: stats.value.pending_contract_number || 0,
        icon: EditPen,
        color: 'warning',
        filter: 'pending_contract_number'
      },
      {
        key: 'completed',
        label: '已编号',
        value: stats.value.completed || 0,
        icon: Check,
        color: 'success',
        filter: 'completed'
      }
    ];
  } else {
    // 管理员卡片：总合同数、已通过、已拒绝、待审核
    return [
      {
        key: 'total',
        label: '总合同数',
        value: stats.value.total || 0,
        icon: Document,
        color: 'primary',
        filter: ''
      },
      {
        key: 'approved',
        label: '已通过',
        value: (stats.value.approved || 0) + (stats.value.completed || 0),
        icon: Check,
        color: 'success',
        filter: 'approved_all'
      },
      {
        key: 'rejected',
        label: '已拒绝',
        value: stats.value.rejected || 0,
        icon: Close,
        color: 'danger',
        filter: 'rejected'
      },
      {
        key: 'pending',
        label: '待审核',
        value: (stats.value.pending || 0) + (stats.value.pending_city_review || 0) + (stats.value.pending_contract_number || 0),
        icon: Clock,
        color: 'warning',
        filter: 'pending_all'
      }
    ];
  }
});

// 设置活动筛选
const setActiveFilter = (filterValue) => {
  activeFilter.value = filterValue;

  // 更新列表筛选条件
  if (
    contractListRef.value &&
    typeof contractListRef.value.handleFilter === "function"
  ) {
    contractListRef.value.statusFilter = filterValue;
    contractListRef.value.handleFilter();
  } else {
    console.warn("ContractList组件未正确加载或handleFilter方法不存在");
  }
};

// 查看合同详情（弹窗方式）
const handleViewContract = (contract) => {
  currentContractId.value = contract.id;
  showDetailDialog.value = true;
};

// 查看合同详情（Tab方式）
const handleViewContractInTab = (contract) => {
  openTab({
    key: `contract-detail-${contract.id}`,
    title: `合同详情 - ${contract.serial_number}`,
    component: "ContractDetailTab",
    icon: "Document",
    params: {
      contractId: contract.id,
      contract: contract,
    },
  });
};

// 编辑合同
const handleEditContract = (contract) => {
  currentContract.value = contract;
  showEditDialog.value = true;
};

// 审核合同
const handleReviewContract = (contract) => {
  currentContract.value = contract;
  showReviewDialog.value = true;
};

// 删除合同
const handleDeleteContract = (contract) => {
  // 删除逻辑在 ContractList 组件中处理，统计数据已在组件内部更新
  // 不再调用 refreshStats()，避免不必要的API请求
};

// 处理选择变化
const handleSelectionChange = (selection) => {
  selectedContracts.value = selection;
};

// 处理行点击 - 直接跳转到Tab页面
const handleRowClick = (row) => {
  handleViewContractInTab(row);
};

// 批量删除
const batchDelete = async () => {
  try {
    await ElMessageBox.confirm(
      `确定要删除选中的 ${selectedContracts.value.length} 个合同吗？`,
      "批量删除确认",
      {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      },
    );

    const deletePromises = selectedContracts.value.map((contract) =>
      deleteContract(contract.id),
    );

    await Promise.all(deletePromises);

    ElMessage.success(`成功删除 ${selectedContracts.value.length} 个合同`);
    selectedContracts.value = [];
    refreshStats();
  } catch (error) {
    if (error !== "cancel") {
      if (import.meta.env.DEV) {
        console.error("批量删除失败:", error);
      }
      ElMessage.error("批量删除失败");
    }
  }
};

// 导出合同数据
const exportContracts = async () => {
  try {
    ElMessage.info("导出功能开发中...");
    // const blob = await contractsAPI.export()
    // 处理文件下载
  } catch (error) {
    if (import.meta.env.DEV) {
      console.error("导出失败:", error);
    }
    ElMessage.error("导出失败");
  }
};

// 处理合同更新
const handleContractUpdated = () => {
  refreshStats();
  // 刷新列表在各个组件内部处理
};

// 处理合同审核
const handleContractReviewed = () => {
  refreshStats();
  // 刷新列表在各个组件内部处理
};

// 刷新统计数据
const refreshStats = async () => {
  await getStats();
};

// 组件挂载时获取统计数据
onMounted(() => {
  refreshStats();
});
</script>

<style scoped>
.contract-manage-page {
  min-height: 100%;
  background: #f5f5f5;
}

.page-header {
  background: #fff;
  border-bottom: 1px solid #e4e7ed;
  padding: 24px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-left {
  flex: 1;
}

.page-title {
  font-size: 24px;
  font-weight: 600;
  color: #303133;
  margin: 0 0 8px;
}

.page-subtitle {
  font-size: 14px;
  color: #909399;
  margin: 0;
}

.header-right {
  display: flex;
  gap: 12px;
}

.stats-section {
  padding: 24px;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
}

.stat-card {
  background: #fff;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  gap: 16px;
  transition: transform 0.3s ease;
  box-sizing: border-box;
  border: 2px solid transparent;
}

.stat-card:hover {
  transform: translateY(-2px);
}

.clickable-card {
  cursor: pointer;
  user-select: none;
  transition: all 0.3s ease;
}

.clickable-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.2);
}

.clickable-card.active {
  border-color: #409eff;
  box-shadow: 0 4px 16px rgba(64, 158, 255, 0.3);
}

.clickable-card.active:hover {
  box-shadow: 0 6px 20px rgba(64, 158, 255, 0.4);
}

.stat-icon {
  width: 56px;
  height: 56px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #fff;
}

.stat-card--primary .stat-icon {
  background: linear-gradient(135deg, #409eff, #337ecc);
}

.stat-card--warning .stat-icon {
  background: linear-gradient(135deg, #e6a23c, #b88230);
}

.stat-card--info .stat-icon {
  background: linear-gradient(135deg, #909399, #73767a);
}

.stat-card--success .stat-icon {
  background: linear-gradient(135deg, #67c23a, #529b2e);
}

.stat-card--danger .stat-icon {
  background: linear-gradient(135deg, #f56c6c, #c45656);
}

.stat-content {
  flex: 1;
}

.stat-value {
  font-size: 24px;
  font-weight: 700;
  color: #303133;
  line-height: 1;
}

.stat-label {
  font-size: 14px;
  color: #606266;
  margin-top: 4px;
}

.page-content {
  padding: 0 24px 24px;
}

.list-card {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .stats-grid {
    grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
    gap: 16px;
  }
}

@media (max-width: 768px) {
  .page-header {
    flex-direction: column;
    gap: 16px;
    align-items: flex-start;
  }

  .header-right {
    width: 100%;
    justify-content: flex-end;
  }

  .stats-section {
    padding: 16px;
  }

  .stats-grid {
    grid-template-columns: 1fr;
    gap: 12px;
  }

  .stat-card {
    padding: 16px;
  }

  .page-content {
    padding: 0 16px 16px;
  }
}
</style>
