<template>
  <div class="statistics-page">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <h1>合同统计</h1>
        <p>查看系统中所有合同的统计数据和趋势分析</p>
      </div>
      <div class="header-actions">
        <el-button type="primary" @click="loadStatistics">
          <el-icon><Refresh /></el-icon>
          刷新数据
        </el-button>
      </div>
    </div>

    <!-- 统计卡片 -->
    <div class="stats-cards">
      <el-row :gutter="20">
        <el-col :span="6">
          <el-card class="stats-card">
            <div class="stats-content">
              <div class="stats-icon total">
                <el-icon><Document /></el-icon>
              </div>
              <div class="stats-info">
                <div class="stats-number">{{ totalContracts }}</div>
                <div class="stats-label">合同总数</div>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stats-card">
            <div class="stats-content">
              <div class="stats-icon pending">
                <el-icon><Clock /></el-icon>
              </div>
              <div class="stats-info">
                <div class="stats-number">{{ pendingContracts }}</div>
                <div class="stats-label">待处理</div>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stats-card">
            <div class="stats-content">
              <div class="stats-icon approved">
                <el-icon><Check /></el-icon>
              </div>
              <div class="stats-info">
                <div class="stats-number">{{ approvedContracts }}</div>
                <div class="stats-label">已完成</div>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stats-card">
            <div class="stats-content">
              <div class="stats-icon rejected">
                <el-icon><Close /></el-icon>
              </div>
              <div class="stats-info">
                <div class="stats-number">{{ rejectedContracts }}</div>
                <div class="stats-label">已拒绝</div>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 图表区域 -->
    <div class="charts-section">
      <el-row :gutter="20">
        <el-col :span="12">
          <el-card>
            <template #header>
              <span>合同趋势</span>
            </template>
            <div class="chart-container">
              <v-chart
                class="chart"
                :option="trendChartOption"
                :loading="chartsLoading"
                autoresize
              />
            </div>
          </el-card>
        </el-col>
        <el-col :span="12">
          <el-card>
            <template #header>
              <span>状态分布</span>
            </template>
            <div class="chart-container">
              <v-chart
                class="chart"
                :option="distributionChartOption"
                :loading="chartsLoading"
                autoresize
              />
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 详细统计表格 -->
    <div class="detailed-stats">
      <el-card>
        <template #header>
          <div class="card-header">
            <span>详细统计</span>
            <el-button type="primary" size="small" @click="exportReport">
              <el-icon><Download /></el-icon>
              导出报告
            </el-button>
          </div>
        </template>

        <el-table :data="detailedStats" style="width: 100%" v-loading="loading">
          <el-table-column prop="period" label="时间段" width="120" />
          <el-table-column prop="total" label="合同总数" width="100" />
          <el-table-column prop="pending" label="待处理" width="100" />
          <el-table-column prop="approved" label="已完成" width="100" />
          <el-table-column prop="rejected" label="已拒绝" width="100" />
          <el-table-column prop="efficiency" label="处理效率" width="120">
            <template #default="scope">
              <el-tag :type="getEfficiencyType(scope.row.efficiency)">
                {{ scope.row.efficiency }}%
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="avgTime" label="平均用时" />
        </el-table>
      </el-card>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, computed } from "vue";
import * as echarts from "echarts";
import VChart from "vue-echarts";
import {
  Document,
  Clock,
  Check,
  Close,
  Download,
  Refresh,
} from "@element-plus/icons-vue";
import { contractsAPI } from "@/api/contracts";
import { statisticsAPI } from "@/api/statistics";
import { ElMessage } from "element-plus";
import { useAuth } from "@/composables/useAuth";

// 认证相关
const { user, userRole, isAuthenticated } = useAuth();

// 响应式数据
const loading = ref(false);
const chartsLoading = ref(false);
const totalContracts = ref(0);
const pendingContracts = ref(0);
const approvedContracts = ref(0);
const rejectedContracts = ref(0);

// 图表配置
const trendChartOption = ref(null);
const distributionChartOption = ref(null);

const detailedStats = ref([]);

// 加载统计数据
const loadStatistics = async () => {
  try {
    loading.value = true;
    chartsLoading.value = true;

    // 管理员直接使用合同统计API，这个API已经验证过数据正确性
    const statsResponse = await contractsAPI.getStats();

    if (statsResponse.success) {
      const data = statsResponse.data;
      console.log('📊 管理员合同统计数据:', data);

      // 管理员统计：使用合同API的准确数据
      totalContracts.value = data.total || 0;

      // 待处理包括：pending（待审核）+ pending_city_review（待市局审核）+ pending_contract_number（待分配编号）
      pendingContracts.value = (data.pending || 0) +
                              (data.pending_city_review || 0) +
                              (data.pending_contract_number || 0);

      // 已完成包括：approved（已通过）+ completed（已完成）
      approvedContracts.value = (data.approved || 0) + (data.completed || 0);

      // 已拒绝
      rejectedContracts.value = data.rejected || 0;

      console.log('📊 处理后的统计数据:', {
        total: totalContracts.value,
        pending: pendingContracts.value,
        approved: approvedContracts.value,
        rejected: rejectedContracts.value
      });

      // 尝试获取趋势数据
      try {
        const trendsResponse = await statisticsAPI.getTrends('month');
        if (trendsResponse.success && trendsResponse.data) {
          setupChartsWithTrendsData(trendsResponse.data, data);
        } else {
          setupChartsWithCurrentStats(data);
        }
      } catch (trendsError) {
        console.warn('获取趋势数据失败，使用当前数据:', trendsError);
        setupChartsWithCurrentStats(data);
      }

      // 设置详细统计数据
      setupDetailedStats(data);
    } else {
      console.error('获取合同统计数据失败:', statsResponse);
      ElMessage.error('获取统计数据失败');
    }
  } catch (error) {
    console.error("加载合同统计数据失败:", error);
    ElMessage.error("加载统计数据失败");
  } finally {
    loading.value = false;
    chartsLoading.value = false;
  }
};

// 设置图表数据（使用趋势数据）
const setupChartsWithTrendsData = (trendsData, summaryData) => {
  console.log('📊 设置管理员图表，趋势数据:', trendsData);

  if (trendsData && trendsData.length > 0) {
    const dates = trendsData.map(item => item.date || item.month);
    const totals = trendsData.map(item => item.total || 0);
    const completed = trendsData.map(item => (item.approved || 0) + (item.completed || 0));

    setupTrendChart({ dates, totals, completed });
  } else {
    setupChartsWithCurrentStats(summaryData);
    return;
  }

  // 分布图表
  const distributionData = [];
  if (pendingContracts.value > 0) distributionData.push({ name: '待处理', value: pendingContracts.value });
  if (approvedContracts.value > 0) distributionData.push({ name: '已完成', value: approvedContracts.value });
  if (rejectedContracts.value > 0) distributionData.push({ name: '已拒绝', value: rejectedContracts.value });

  setupDistributionChart({ distribution: distributionData });
};

// 设置图表数据（兜底方案）
const setupChartsWithCurrentStats = (data) => {
  // 趋势图表 - 基于当前数据模拟趋势
  const currentMonth = new Date().toISOString().slice(0, 7);

  const trendData = {
    dates: [currentMonth],
    totals: [totalContracts.value],
    completed: [approvedContracts.value]
  };

  setupTrendChart(trendData);

  // 分布图表
  const distributionData = [];
  if (pendingContracts.value > 0) distributionData.push({ name: '待处理', value: pendingContracts.value });
  if (approvedContracts.value > 0) distributionData.push({ name: '已完成', value: approvedContracts.value });
  if (rejectedContracts.value > 0) distributionData.push({ name: '已拒绝', value: rejectedContracts.value });

  setupDistributionChart({ distribution: distributionData });
};

// 设置趋势图表
const setupTrendChart = (data) => {
  trendChartOption.value = {
    title: {
      text: '合同处理趋势',
      left: 'center'
    },
    tooltip: {
      trigger: 'axis'
    },
    legend: {
      data: ['合同总数', '已完成'],
      top: 30
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: data.dates
    },
    yAxis: {
      type: 'value'
    },
    series: [
      {
        name: '合同总数',
        type: 'line',
        data: data.totals,
        smooth: true
      },
      {
        name: '已完成',
        type: 'line',
        data: data.completed,
        smooth: true
      }
    ]
  };
};

// 设置分布图表
const setupDistributionChart = (data) => {
  distributionChartOption.value = {
    title: {
      text: '合同状态分布',
      left: 'center'
    },
    tooltip: {
      trigger: 'item'
    },
    legend: {
      orient: 'vertical',
      left: 'left'
    },
    series: [
      {
        name: '合同状态',
        type: 'pie',
        radius: '50%',
        data: data.distribution,
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: 'rgba(0, 0, 0, 0.5)'
          }
        }
      }
    ]
  };
};

// 设置详细统计数据
const setupDetailedStats = (stats) => {
  const efficiency = totalContracts.value > 0
    ? Math.round(((approvedContracts.value + rejectedContracts.value) / totalContracts.value) * 100)
    : 0;

  detailedStats.value = [
    {
      period: "当前统计",
      total: totalContracts.value,
      pending: pendingContracts.value,
      approved: approvedContracts.value,
      rejected: rejectedContracts.value,
      efficiency: efficiency,
      avgTime: "暂无数据",
    }
  ];
};

// 获取效率标签类型
const getEfficiencyType = (efficiency) => {
  if (efficiency >= 80) return "success";
  if (efficiency >= 60) return "warning";
  return "danger";
};

// 导出报告
const exportReport = () => {
  ElMessage.info("导出功能开发中...");
};

// 组件挂载时加载数据
onMounted(() => {
  loadStatistics();
});
</script>

<style scoped>
/* 复用 StatisticsPage 的样式 */
.statistics-page {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.header-content h1 {
  margin: 0 0 8px 0;
  font-size: 24px;
  font-weight: 600;
  color: #303133;
}

.header-content p {
  margin: 0;
  color: #606266;
  font-size: 14px;
}

.stats-cards {
  margin-bottom: 20px;
}

.stats-card {
  height: 120px;
}

.stats-content {
  display: flex;
  align-items: center;
  height: 100%;
}

.stats-icon {
  width: 60px;
  height: 60px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16px;
  font-size: 24px;
  color: white;
}

.stats-icon.total { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); }
.stats-icon.pending { background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%); }
.stats-icon.approved { background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%); }
.stats-icon.rejected { background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%); }

.stats-info {
  flex: 1;
}

.stats-number {
  font-size: 28px;
  font-weight: 600;
  color: #303133;
  line-height: 1;
  margin-bottom: 4px;
}

.stats-label {
  font-size: 14px;
  color: #909399;
}

.charts-section {
  margin-bottom: 20px;
}

.chart-container {
  height: 300px;
}

.chart {
  height: 100%;
  width: 100%;
}

.detailed-stats .card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
</style>
